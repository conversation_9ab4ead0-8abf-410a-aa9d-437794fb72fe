#!/usr/bin/env elixir

# Debug script to understand key ordering issues
Mix.install([{:drops, path: "."}])

# Test the map literal order
map = %{
  name: :string,
  email: :string
}

# Test with different key order (alphabetically reversed)
map2 = %{
  zebra: :string,
  apple: :string
}

IO.puts("Original map keys: #{inspect(Map.keys(map))}")
IO.puts("Enum.to_list(map): #{inspect(Enum.to_list(map))}")

IO.puts("Map2 keys: #{inspect(Map.keys(map2))}")
IO.puts("Enum.to_list(map2): #{inspect(Enum.to_list(map2))}")

# Test inference with map2
inferred2 = Drops.Schema.Inference.infer_schema(map2, [])
IO.puts("Inferred2 schema (list): #{inspect(inferred2)}")
IO.puts("Inferred2 keys: #{inspect(Enum.map(inferred2, fn {key, _} -> key end))}")

# Test Map.new behavior
list_pairs = [{{:required, :name}, {:type, {:string, []}}}, {{:required, :email}, {:type, {:string, []}}}]
IO.puts("List pairs: #{inspect(list_pairs)}")
new_map = Map.new(list_pairs)
IO.puts("Map.new result keys: #{inspect(Map.keys(new_map))}")
IO.puts("Map.new result: #{inspect(Enum.to_list(new_map))}")

# Test the inference process
inferred = Drops.Schema.Inference.infer_schema(map, [])
IO.puts("Inferred schema (list): #{inspect(inferred)}")
IO.puts("Inferred keys: #{inspect(Enum.map(inferred, fn {key, _} -> key end))}")

# Test the compilation process
compiled = Drops.Type.Compiler.visit(inferred, [])
IO.puts("Compiled keys: #{inspect(Enum.map(compiled.keys, & &1.path))}")
